import time
from twitchio.ext import commands
import json
import asyncio
import requests
import websockets
import logging
from datetime import datetime, timedelta
from typing import Optional


# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TwitchBot(commands.Bot):

    def __init__(self):
        with open("config.json") as config_file:
            config = json.load(config_file)
        
        self.oauth_token = config["oauth_token"]
        self.client_id = config["client_id"]
        self.refresh_token = config.get("refresh_token")
        self.streamers = config["streamers"]
        self.mod_overrides = config.get("mod_overrides", ["felixtm_"])
        
        # Use the OAuth token for API calls (no client_secret needed)
        self.api_token = self.oauth_token
        self.token_expires_at = datetime.now() + timedelta(seconds=3600)  # Default expiry
        
        # Validate the token on startup
        if not self.validate_token():
            logger.info("Token invalid or expired, attempting to refresh...")
            if not self.refresh_oauth_token():
                logger.error("Failed to refresh token. Please update your OAuth token in config.json")
                raise Exception("Failed to refresh OAuth token")

        super().__init__(token=self.oauth_token, prefix="!", initial_channels=self.streamers)

        # Initialize status tracker for streamers with stream start time
        self.stream_status = {
            streamer: {
                'live': False, 
                'title': '', 
                'game': '',
                'stream_start_time': None
            } for streamer in self.streamers
        }

        # EventSub connection management
        self.eventsub_connection = None
        self.reconnect_delay = 5
        self.max_reconnect_delay = 300

        # Subscription tracking
        self.subscription_ids = {}  # key: f"{streamer}_{event_type}" -> subscription_id
        self.subscription_to_streamer = {}  # subscription_id -> (streamer, event_type)
        self.subscription_queue = []
        self.subscription_semaphore = asyncio.Semaphore(1)  # Process one subscription at a time
        self.last_subscription_time = 0
        self.subscription_delay = 1.0  # Wait 1 second between subscriptions
        
        # Track subscription costs (Twitch limits)
        self.current_subscription_cost = 0
        self.max_subscription_cost = 10  # Twitch's limit per WebSocket connection

        # Cost per subscription type
        self.subscription_costs = {
            'stream.online': 1,
            'stream.offline': 1,
            'channel.update': 1
        }
        # EventSub task management
        self._eventsub_task = None
        self._eventsub_stop = False
        self.current_session_id = None
        self.subscription_session = {}

        # Health check tracking
        self.last_keepalive_time = None
        self.connection_established_time = None
        self.last_notification_time = None
        self.health_check_interval = 300  # 5 minutes


        # Dynamic messages for notifications
        self.notification_messages = {
            'live': lambda streamer: f"{streamer} ist jetzt live! Pag",
            'offline': lambda streamer, uptime=None: f"{streamer} ist offline gegangen{(' nach ' + uptime) if uptime else ''}. FeelsBadMan",
            'title': lambda streamer, new_title: f"{streamer} hat den Titel geändert! Neuer Titel PauseChamp 👉 {new_title}  ",
            'game': lambda streamer, new_game: f"{streamer} hat die Kategorie geändert PauseChamp 👉 {new_game}!"
        }

        # Command messages
        self.command_messages = {
            'add_notification_live': lambda user, streamer: f"@{user}, du wirst benachrichtigt, wenn {streamer} live geht!",
            'add_notification_offline': lambda user, streamer: f"@{user}, du wirst benachrichtigt, wenn {streamer} offline geht!",
            'add_notification_title': lambda user, streamer: f"{user}, du wirst benachrichtigt, wenn {streamer} den Titel ändert!",
            'add_notification_game': lambda user, streamer: f"{user}, du wirst benachrichtigt, wenn {streamer} das Spiel wechselt!",
            'already_notified_live': lambda user, streamer: f"{user}, du wirst bereits benachrichtigt, wenn {streamer} live geht!",
            'already_notified_offline': lambda user, streamer: f"{user}, du wirst bereits benachrichtigt, wenn {streamer} offline geht!",
            'already_notified_title': lambda user, streamer: f"{user}, du wirst bereits benachrichtigt, wenn {streamer} den Titel ändert!",
            'already_notified_game': lambda user, streamer: f"{user}, du wirst bereits benachrichtigt, wenn {streamer} das Spiel wechselt!",
            'invalid_type': lambda notification_type: f"Ungültiger Benachrichtigungstyp: {notification_type}. Versuche: live, offline, title, game.",
            'add_all': lambda user, streamer: f"{user}, du wirst über alle Aktivitäten von {streamer} benachrichtigt!",
            'remove_notification': lambda user, streamer, notification_type: f"{user}, du wirst nicht mehr benachrichtigt, wenn {streamer} {notification_type}.",
            'remove_all_notifications': lambda user, streamer: f"{user}, du wirst nicht mehr über Aktivitäten von {streamer} benachrichtigt!"
        }

    def is_mod(self, ctx):
        if ctx.author.name.lower() in [name.lower() for name in self.mod_overrides]:
            return True
        return ctx.author.is_mod

    def calculate_subscription_cost(self, streamers, events_per_streamer=3):
        """Calculate total subscription cost"""
        return len(streamers) * events_per_streamer

    def prioritize_streamers(self, streamers):
        """Prioritize streamers based on importance or activity"""
        # You can customize this logic based on your needs
        # For example, prioritize streamers that are currently live
        # or have higher activity/importance
        
        live_streamers = [s for s in streamers if self.stream_status[s]['live']]
        offline_streamers = [s for s in streamers if not self.stream_status[s]['live']]
        
        # Prioritize live streamers first
        return live_streamers + offline_streamers

    def select_streamers_for_subscription(self, max_cost=10):
        """Select streamers to subscribe to based on cost limits"""
        events_per_streamer = 3  # stream.online, stream.offline, channel.update
        max_streamers = max_cost // events_per_streamer
        
        prioritized_streamers = self.prioritize_streamers(self.streamers)
        selected_streamers = prioritized_streamers[:max_streamers]
        
        if len(selected_streamers) < len(self.streamers):
            skipped = len(self.streamers) - len(selected_streamers)
            logger.warning(f"Rate limit constraint: Subscribing to {len(selected_streamers)} streamers, skipping {skipped}")
            logger.info(f"Selected streamers: {selected_streamers}")
            logger.info(f"Skipped streamers: {prioritized_streamers[max_streamers:]}")
        
        return selected_streamers

    def validate_token(self):
        """Validate the current OAuth token"""
        url = "https://id.twitch.tv/oauth2/validate"
        headers = {
            'Authorization': f'Bearer {self.api_token}'
        }
        try:
            response = requests.get(url, headers=headers, timeout=10)
            if response.status_code == 200:
                token_info = response.json()
                expires_in = token_info.get('expires_in')
                if expires_in:
                    self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                logger.info(f"Token valid. Expires in {expires_in} seconds")
                return True
            else:
                logger.warning(f"Token validation failed: {response.status_code}")
                return False
        except requests.RequestException as e:
            logger.error(f"Failed to validate token: {e}")
            return False

    def refresh_oauth_token(self):
        """Refresh the OAuth token using the refresh token"""
        if not self.refresh_token:
            logger.error("No refresh token available")
            return False
            
        url = "https://id.twitch.tv/oauth2/token"
        data = {
            'client_id': self.client_id,
            'refresh_token': self.refresh_token,
            'grant_type': 'refresh_token'
        }
        try:
            response = requests.post(url, data=data, timeout=10)
            response.raise_for_status()
            response_data = response.json()
            
            if 'access_token' in response_data:
                self.api_token = response_data['access_token']
                self.oauth_token = self.api_token
                
                if 'refresh_token' in response_data:
                    self.refresh_token = response_data['refresh_token']
                    self.update_config_tokens()
                
                expires_in = response_data.get('expires_in', 3600)
                self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                
                logger.info("Successfully refreshed OAuth token")
                return True
            else:
                logger.error(f"No access token in refresh response: {response_data}")
                return False
        except requests.RequestException as e:
            logger.error(f"Failed to refresh token: {e}")
            return False

    def update_config_tokens(self):
        """Update the config.json file with new tokens"""
        try:
            with open("config.json", 'r') as f:
                config = json.load(f)
            
            config["oauth_token"] = self.oauth_token
            if self.refresh_token:
                config["refresh_token"] = self.refresh_token
            
            with open("config.json", 'w') as f:
                json.dump(config, f, indent=4)
            
            logger.info("Updated config.json with new tokens")
        except Exception as e:
            logger.error(f"Failed to update config.json: {e}")

    def get_user_id(self, streamer):
        """Get user ID for a streamer"""
        self.refresh_token_if_needed()
        url = f"https://api.twitch.tv/helix/users?login={streamer}"
        headers = {
            'Authorization': f'Bearer {self.api_token}',
            'Client-Id': self.client_id
        }
        try:
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            user_data = response.json()
            if 'data' in user_data and user_data['data']:
                return user_data['data'][0]['id']
        except requests.RequestException as e:
            logger.error(f"Failed to get user ID for {streamer}: {e}")
        return None

    def refresh_token_if_needed(self):
        """Refresh the OAuth token if it's about to expire"""
        if datetime.now() >= self.token_expires_at - timedelta(minutes=5):
            logger.info("Token is about to expire, refreshing...")
            if self.refresh_oauth_token():
                logger.info("Token refreshed successfully")
            else:
                logger.error("Failed to refresh token")
                if not self.validate_token():
                    logger.error("Current token is also invalid")

    async def get_stream_start_time(self, streamer):
        """Get stream start time from Twitch API"""
        self.refresh_token_if_needed()
        url = f"https://api.twitch.tv/helix/streams?user_login={streamer}"
        headers = {
            'Authorization': f'Bearer {self.api_token}',
            'Client-Id': self.client_id
        }
        try:
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            stream_data = response.json()
            
            if stream_data.get('data'):
                stream_info = stream_data['data'][0]
                started_at = stream_info.get('started_at')
                if started_at:
                    return datetime.fromisoformat(started_at.replace('Z', '+00:00')).replace(tzinfo=None)
            
            return None
        except requests.RequestException as e:
            logger.error(f"Failed to get stream start time for {streamer}: {e}")
            return None

    async def queue_subscription(self, session_id, streamer, user_id, event_type):
        """Add subscription to queue for rate-limited processing"""
        subscription_item = {
            'session_id': session_id,
            'streamer': streamer,
            'user_id': user_id,
            'event_type': event_type
        }
        self.subscription_queue.append(subscription_item)

    async def process_subscription_queue(self):
        """Process subscription queue with rate limiting"""
        while self.subscription_queue:
            async with self.subscription_semaphore:
                if not self.subscription_queue:
                    break
                
                item = self.subscription_queue.pop(0)
                
                # Check if we've exceeded cost limits
                cost = self.subscription_costs.get(item['event_type'], 1)
                if self.current_subscription_cost + cost > self.max_subscription_cost:
                    logger.warning(f"Subscription cost limit reached. Skipping {item['streamer']} {item['event_type']}")
                    continue
                
                # Rate limiting: wait between subscriptions
                current_time = time.time()
                time_since_last = current_time - self.last_subscription_time
                if time_since_last < self.subscription_delay:
                    wait_time = self.subscription_delay - time_since_last
                    logger.debug(f"Rate limiting: waiting {wait_time:.2f}s before next subscription")
                    await asyncio.sleep(wait_time)
                
                success = await self.create_eventsub_subscription(
                    item['session_id'],
                    item['streamer'], 
                    item['user_id'],
                    item['event_type']
                )
                
                if success:
                    # Increase cost only on success
                    self.current_subscription_cost += cost
                    self.last_subscription_time = time.time()
                else:
                    logger.debug(f"Subscription failed for {item['streamer']} {item['event_type']}")
                
                # Additional safety delay for Twitch's rate limits
                await asyncio.sleep(0.5)

    def format_uptime(self, start_time, end_time):
        """Format uptime in 'Xstd Ymin' format"""
        if not start_time or not end_time:
            return "unbekannt"
        
        duration = end_time - start_time
        total_seconds = int(duration.total_seconds())
        
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        
        if hours > 0:
            return f"{hours}std {minutes}min"
        else:
            return f"{minutes}min"

    async def create_eventsub_subscription(self, session_id, streamer, user_id, event_type):
        """Create EventSub subscription for a specific event type"""
        self.refresh_token_if_needed()
        
        subscription_data = {
            "type": event_type,
            "version": "1" if event_type in ["stream.online", "stream.offline"] else "2",
            "condition": {
                "broadcaster_user_id": user_id
            },
            "transport": {
                "method": "websocket",
                "session_id": session_id
            }
        }

        headers = {
            'Client-ID': self.client_id,
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json'
        }

        try:
            response = requests.post(
                'https://api.twitch.tv/helix/eventsub/subscriptions',
                headers=headers, 
                data=json.dumps(subscription_data),
                timeout=10
            )
            
            if response.status_code == 202:
                subscription_data = response.json()
                subscription_id = subscription_data['data'][0]['id']
                
                # Track the subscription
                key = f"{streamer}_{event_type}"
                self.subscription_ids[key] = subscription_id
                self.subscription_to_streamer[subscription_id] = (streamer, event_type)
                # track which session this subscription belongs to
                self.subscription_session[subscription_id] = session_id
                
                logger.info(f"Successfully subscribed to {event_type} for {streamer} (ID: {subscription_id})")
                return True
            else:
                if response.status_code == 429:
                    # Provide extra debug info on websocket transports
                    subs = await self.get_eventsub_subscriptions()
                    session_ids = {s.get('transport', {}).get('session_id') for s in subs if s.get('transport', {}).get('method') == 'websocket' and s.get('transport', {}).get('session_id')}
                    logger.error(f"Failed to subscribe to {event_type} for {streamer}: {response.status_code} {response.text}. Active websocket transports={len(session_ids)} sessions={list(session_ids)}")
                else:
                    logger.error(f"Failed to subscribe to {event_type} for {streamer}: {response.status_code} {response.text}")
                return False
        except requests.RequestException as e:
            logger.error(f"Request failed for {event_type} subscription for {streamer}: {e}")
            return False

    async def handle_eventsub_message(self, message_data):
        """Handle incoming EventSub messages"""
        message_type = message_data['metadata']['message_type']
        
        if message_type == 'notification':
            subscription_id = message_data['payload']['subscription']['id']
            
            if subscription_id in self.subscription_to_streamer:
                streamer, event_type = self.subscription_to_streamer[subscription_id]
            else:
                logger.warning(f"Received notification for unknown subscription ID: {subscription_id}")
                return
            
            event = message_data['payload']['event']
            subscription_type = message_data['metadata']['subscription_type']
            
            logger.info(f"Received {subscription_type} event for {streamer}")
            
            if subscription_type == 'stream.online':
                if not self.stream_status[streamer]['live']:
                    self.stream_status[streamer]['live'] = True
                    self.stream_status[streamer]['stream_start_time'] = datetime.now()
                    self.last_notification_time = datetime.now()
                    await self.notify_users(streamer, 'live')
                    logger.info(f"{streamer} went live at {self.stream_status[streamer]['stream_start_time']}")

            elif subscription_type == 'stream.offline':
                if self.stream_status[streamer]['live']:
                    stream_end_time = datetime.now()
                    uptime = None
                    
                    if self.stream_status[streamer]['stream_start_time']:
                        uptime = self.format_uptime(
                            self.stream_status[streamer]['stream_start_time'], 
                            stream_end_time
                        )
                    
                    self.stream_status[streamer]['live'] = False
                    self.stream_status[streamer]['stream_start_time'] = None
                    self.last_notification_time = datetime.now()

                    await self.notify_users(streamer, 'offline', uptime=uptime)
                    uptime_str = f" after {uptime}" if uptime else ""
                    logger.info(f"{streamer} went offline{uptime_str}")

            elif subscription_type == 'channel.update':
                new_title = event.get('title', '')
                new_game = event.get('category_name', '')

                if new_title != self.stream_status[streamer]['title']:
                    logger.info(f"{streamer} changed title to: {new_title}")
                    self.last_notification_time = datetime.now()
                    await self.notify_users(streamer, 'title', new_title=new_title)
                    self.stream_status[streamer]['title'] = new_title

                if new_game != self.stream_status[streamer]['game']:
                    logger.info(f"{streamer} changed game to: {new_game}")
                    self.last_notification_time = datetime.now()
                    await self.notify_users(streamer, 'game', new_game=new_game)
                    self.stream_status[streamer]['game'] = new_game
                    
        elif message_type == 'session_keepalive':
            logger.debug(f"Keepalive received")
            self.last_keepalive_time = datetime.now()

        elif message_type == 'session_reconnect':
            reconnect_url = message_data['payload']['session']['reconnect_url']
            logger.info(f"Reconnect requested to {reconnect_url}")
            
        elif message_type == 'revocation':
            subscription_id = message_data['payload']['subscription']['id']
            if subscription_id in self.subscription_to_streamer:
                streamer, event_type = self.subscription_to_streamer[subscription_id]
                logger.warning(f"Subscription revoked for {streamer} ({event_type}): {message_data['payload']}")
                del self.subscription_to_streamer[subscription_id]
                key = f"{streamer}_{event_type}"
                if key in self.subscription_ids:
                    del self.subscription_ids[key]
                    # Decrease subscription cost
                    cost = self.subscription_costs.get(event_type, 1)
                    self.current_subscription_cost = max(0, self.current_subscription_cost - cost)
                if subscription_id in self.subscription_session:
                    del self.subscription_session[subscription_id]

    async def connect_eventsub(self):
        """Connect to EventSub WebSocket for all streamers.

        This function runs as a single long-lived task and will reconnect with
        exponential backoff. It attempts to avoid opening multiple concurrent
        websocket connections for the same token.
        """
        # single task loop
        self._eventsub_stop = False
        backoff = self.reconnect_delay or 5

        while not self._eventsub_stop:
            try:
                logger.info("Opening EventSub websocket connection...")
                async with websockets.connect('wss://eventsub.wss.twitch.tv/ws') as ws:
                    logger.info("Connected to EventSub WebSocket")
                    self.eventsub_connection = ws
                    self.connection_established_time = datetime.now()

                    # Reset reconnect/backoff when connection succeeds
                    backoff = self.reconnect_delay = 5
                    self.current_subscription_cost = 0
                    self.subscription_queue.clear()

                    # Receive welcome message
                    welcome_msg = await ws.recv()
                    welcome_data = json.loads(welcome_msg)

                    if welcome_data.get('metadata', {}).get('message_type') != 'session_welcome':
                        logger.error(f"Unexpected welcome message: {welcome_data}")
                        # close and retry
                        continue

                    session_id = welcome_data['payload']['session']['id']
                    self.current_session_id = session_id
                    logger.info(f"EventSub session established with ID: {session_id}")

                    # Check existing subscriptions/transports before creating new subs
                    subs = await self.get_eventsub_subscriptions()
                    # log overview
                    await asyncio.get_event_loop().run_in_executor(None, self._log_eventsub_overview, subs)

                    # Count unique websocket transports for this token
                    session_ids = {s.get('transport', {}).get('session_id') for s in subs if s.get('transport', {}).get('method') == 'websocket' and s.get('transport', {}).get('session_id')}
                    num_transports = len(session_ids)
                    logger.info(f"Active websocket transports for token: {num_transports} (sessions: {list(session_ids)})")

                    if num_transports >= 3:
                        logger.warning("Websocket transport limit reached. Attempting to cleanup stale websocket subscriptions.")
                        # try to delete stale websocket subscriptions to free slots
                        deleted = await self.cleanup_stale_websocket_subscriptions(max_keep_sessions=2)
                        logger.info(f"Deleted {deleted} stale subscriptions. Re-checking transports...")
                        subs = await self.get_eventsub_subscriptions()
                        session_ids = {s.get('transport', {}).get('session_id') for s in subs if s.get('transport', {}).get('method') == 'websocket' and s.get('transport', {}).get('session_id')}
                        num_transports = len(session_ids)
                        logger.info(f"After cleanup websocket transports: {num_transports} (sessions: {list(session_ids)})")
                        if num_transports >= 3:
                            logger.error("Cannot create new websocket subscriptions: transport limit still reached. Closing connection and backing off.")
                            # Close the ws and backoff
                            try:
                                await ws.close()
                            except Exception:
                                pass
                            await asyncio.sleep(backoff)
                            backoff = min(backoff * 2, self.max_reconnect_delay)
                            continue

                    # Select streamers and queue subscriptions
                    selected_streamers = self.select_streamers_for_subscription(self.max_subscription_cost)
                    events_to_subscribe = ['stream.online', 'stream.offline', 'channel.update']
                    for streamer in selected_streamers:
                        user_id = self.get_user_id(streamer)
                        if not user_id:
                            logger.error(f"Failed to get user ID for {streamer}")
                            continue

                        for event_type in events_to_subscribe:
                            await self.queue_subscription(session_id, streamer, user_id, event_type)

                    # Process subscription queue
                    await self.process_subscription_queue()

                    # Listen for messages until the connection closes
                    async for message in ws:
                        try:
                            message_data = json.loads(message)
                            # Provide richer logging about message types
                            mtype = message_data.get('metadata', {}).get('message_type')
                            sub_type = message_data.get('metadata', {}).get('subscription_type')
                            logger.debug(f"EventSub message received: type={mtype}, subscription_type={sub_type}")
                            await self.handle_eventsub_message(message_data)
                        except Exception as e:
                            logger.error(f"Error handling EventSub message: {e}")
                            logger.debug(message)

            except websockets.exceptions.ConnectionClosed as e:
                logger.warning(f"EventSub connection closed: {e}")
            except websockets.exceptions.WebSocketException as e:
                logger.error(f"EventSub WebSocket error: {e}")
            except Exception as e:
                logger.error(f"Unexpected error in EventSub connection: {e}")
            finally:
                # Clean up local session state and optionally delete subscriptions created with the now-stale session
                if self.current_session_id:
                    logger.info(f"Cleaning up subscriptions associated with session {self.current_session_id}")
                    # Attempt to delete subscriptions that we created for this session to free transports
                    to_delete = [sid for sid, s_session in self.subscription_session.items() if s_session == self.current_session_id]
                    for sub_id in to_delete:
                        try:
                            await self.delete_eventsub_subscription(sub_id)
                        except Exception as e:
                            logger.debug(f"Failed to delete subscription {sub_id}: {e}")

                self.eventsub_connection = None
                self.current_session_id = None
                # reset local mappings
                self.subscription_session = {}
                self.subscription_ids.clear()
                self.subscription_to_streamer.clear()
                self.current_subscription_cost = 0

                if self._eventsub_stop:
                    logger.info("EventSub task requested to stop; exiting connect loop.")
                    break

                logger.info(f"Reconnecting to EventSub in {backoff} seconds")
                await asyncio.sleep(backoff)
                backoff = min(backoff * 2, self.max_reconnect_delay)

    async def start_eventsub_connection(self):
        """Start EventSub connection as a single managed background task."""
        if getattr(self, "_eventsub_task", None) and not self._eventsub_task.done():
            logger.info("EventSub task already running; not starting another.")
            return
        self._eventsub_stop = False
        self._eventsub_task = asyncio.create_task(self.connect_eventsub())

        # Start periodic health check
        asyncio.create_task(self.periodic_health_check())

    async def periodic_health_check(self):
        """Run periodic health checks and log warnings for issues"""
        while not self._eventsub_stop:
            try:
                await asyncio.sleep(self.health_check_interval)

                health = await self.health_check()

                if health['overall_status'] in ['WARNING', 'CRITICAL', 'ERROR']:
                    logger.warning(f"Health Check Alert - Status: {health['overall_status']}")
                    for issue in health['issues']:
                        logger.warning(f"Issue: {issue}")
                    for rec in health['recommendations']:
                        logger.info(f"Recommendation: {rec}")

                    # Auto-restart connection if critical issues detected
                    if health['overall_status'] == 'CRITICAL' and health['connection'].get('status') == 'DISCONNECTED':
                        logger.warning("Critical health issues detected, attempting to restart EventSub connection")
                        await self.start_eventsub_connection()

                elif health['overall_status'] == 'HEALTHY':
                    logger.debug("Periodic health check: All systems healthy")

            except Exception as e:
                logger.error(f"Periodic health check failed: {e}")

            await asyncio.sleep(60)  # Additional delay between checks

    async def get_initial_stream_status(self):
        """Get initial stream status for all streamers using API"""
        for streamer in self.streamers:
            try:
                url = f"https://api.twitch.tv/helix/streams?user_login={streamer}"
                headers = {
                    'Authorization': f'Bearer {self.api_token}',
                    'Client-Id': self.client_id
                }
                response = requests.get(url, headers=headers, timeout=10)
                stream_data = response.json()

                user_id = self.get_user_id(streamer)
                if user_id:
                    channel_url = f"https://api.twitch.tv/helix/channels?broadcaster_id={user_id}"
                    channel_response = requests.get(channel_url, headers=headers, timeout=10)
                    channel_data = channel_response.json()
                else:
                    channel_data = {'data': []}

                if stream_data.get('data'):
                    stream_info = stream_data['data'][0]
                    stream_start_time = await self.get_stream_start_time(streamer)
                    
                    self.stream_status[streamer] = {
                        'live': True,
                        'title': stream_info.get('title', ''),
                        'game': stream_info.get('game_name', ''),
                        'stream_start_time': stream_start_time
                    }
                    
                    if stream_start_time:
                        current_uptime = self.format_uptime(stream_start_time, datetime.now())
                        logger.info(f"{streamer} is currently live for {current_uptime}")
                    else:
                        logger.warning(f"{streamer} is live but couldn't get start time")
                elif channel_data.get('data'):
                    channel_info = channel_data['data'][0]
                    self.stream_status[streamer] = {
                        'live': False,
                        'title': channel_info.get('title', ''),
                        'game': channel_info.get('game_name', ''),
                        'stream_start_time': None
                    }
                else:
                    self.stream_status[streamer] = {
                        'live': False,
                        'title': '',
                        'game': '',
                        'stream_start_time': None
                    }
                    
                logger.info(f"Initial status for {streamer}: {self.stream_status[streamer]}")
                
            except Exception as e:
                logger.error(f"Failed to get initial status for {streamer}: {e}")

    async def event_ready(self):
        logger.info(f"Logged in as {self.nick}")
        await self.get_initial_stream_status()
        await self.start_eventsub_connection()

    async def event_message(self, message):
        if not message.author:
            return

        if message.author.name.lower() == "streamelements" and "OUAH? 😯  Xeno ist live und ich habe nicht mal mitbekommen??? 😔" in message.content:
            logger.info(f"Sending response to StreamElements in {message.channel.name}")
            await asyncio.sleep(5)
            await message.channel.send("@StreamElements, Keine Sorge mein nutzloser Freund! STARMAN benutze einfach den Command !notification in kombination mit: all, live, offline, title, game um bei entsprechener aktivität benachrichtigt zu werden!")
            return

        if message.author.name.lower() == self.nick.lower():
            return

        await self.handle_commands(message)

    # Command handlers
    @commands.command(name='notification')
    async def notification(self, ctx, *, notification_type: Optional[str] = None):
        streamer = ctx.channel.name
        file_name = f"{streamer}_notifications.json"

        try:
            with open(file_name, 'r') as f:
                notifications = json.load(f)
        except FileNotFoundError:
            notifications = {}

        user = ctx.author.name

        if not notification_type:
            await ctx.send(self.command_messages['invalid_type'](''))
            return

        if notification_type == 'all':
            notifications[user] = ['live', 'offline', 'title', 'game']
            await ctx.send(self.command_messages['add_all'](user, streamer))
        else:
            if notification_type not in ['live', 'offline', 'title', 'game']:
                await ctx.send(self.command_messages['invalid_type'](notification_type))
                return

            if user not in notifications:
                notifications[user] = []

            if notification_type not in notifications[user]:
                notifications[user].append(notification_type)

                if notification_type == 'live':
                    await ctx.send(self.command_messages['add_notification_live'](user, streamer))
                elif notification_type == 'offline':
                    await ctx.send(self.command_messages['add_notification_offline'](user, streamer))
                elif notification_type == 'title':
                    await ctx.send(self.command_messages['add_notification_title'](user, streamer))
                elif notification_type == 'game':
                    await ctx.send(self.command_messages['add_notification_game'](user, streamer))
            else:
                if notification_type == 'live':
                    await ctx.send(self.command_messages['already_notified_live'](user, streamer))
                elif notification_type == 'offline':
                    await ctx.send(self.command_messages['already_notified_offline'](user, streamer))
                elif notification_type == 'title':
                    await ctx.send(self.command_messages['already_notified_title'](user, streamer))
                elif notification_type == 'game':
                    await ctx.send(self.command_messages['already_notified_game'](user, streamer))

        with open(file_name, 'w') as f:
            json.dump(notifications, f)

    async def notify_users(self, streamer, notification_type, new_title=None, new_game=None, uptime=None):
        user_mentions = []
        file_name = f"{streamer}_notifications.json"

        try:
            with open(file_name, 'r') as f:
                notifications = json.load(f)
        except FileNotFoundError:
            notifications = {}
    
        for user, types in notifications.items():
            if notification_type in types:
                user_mentions.append(f"@{user}")
                
        channel = self.get_channel(streamer)
        if user_mentions and channel:
            mentions = " ".join(user_mentions)
            if notification_type == 'live':
                await channel.send(f"{self.notification_messages['live'](streamer)}")
                await channel.send(f"{mentions}")
            elif notification_type == 'offline':
                await channel.send(f"{self.notification_messages['offline'](streamer, uptime)}")
                await channel.send(f"{mentions}")
            elif notification_type == 'title':
                await channel.send(f"{self.notification_messages['title'](streamer, new_title)}")
                await channel.send(f"{mentions}")
            elif notification_type == 'game':
                await channel.send(f"{self.notification_messages['game'](streamer, new_game)}")
                await channel.send(f"{mentions}")

    @commands.command(name="sr")
    async def sr_command(self, ctx):
        if not (ctx.author.is_mod or ctx.author.is_vip):
            await ctx.send(f"@{ctx.author.name}, Huh? 🤔🎯 Wirklich? Du hast den Song-Request-Command ausprobiert? 🫵😆🎶 Sorry, aber der ist nur für Moderatoren & VIPs. 🙄🛑👑 Wir müssen natürlich nichts zahlen! 😎💅 Aber hey, wenn du unbedingt willst, kannst du ja deine 250 Kanalpunkte 💰🔥 dafür ausgeben 😏💸 certified skill issue 🤷‍♂️🎉")

    @commands.command(name="eventsub_status")
    async def eventsub_status(self, ctx):
        """Command to check EventSub connection status"""
        if not self.is_mod(ctx):
            return
            
        connection_status = "connected" if self.eventsub_connection else "disconnected"
        subscription_count = len(self.subscription_ids)
        expected_subscriptions = len(self.streamers) * 3  # 3 events per streamer
        
        await ctx.send(f"EventSub Status: {connection_status}, Subscriptions: {subscription_count}/{expected_subscriptions}")

    # --- EventSub helper methods for robust management ---
    def _log_eventsub_overview(self, subs=None):
        """Log a compact overview of current EventSub subscriptions and websocket transports."""
        try:
            if subs is None:
                subs = self.get_eventsub_subscriptions_sync()
            session_ids = {s.get('transport', {}).get('session_id') for s in subs if s.get('transport', {}).get('method') == 'websocket' and s.get('transport', {}).get('session_id')}
            active_sessions = [sid for sid in session_ids if sid]
            logger.info(f"EventSub overview: total_subscriptions={len(subs)}, websocket_transports={len(active_sessions)}, sessions={active_sessions}")
        except Exception as e:
            logger.debug(f"Failed to log EventSub overview: {e}")

    def get_eventsub_subscriptions_sync(self):
        """Synchronous wrapper to fetch all EventSub subscriptions (used for logging)."""
        self.refresh_token_if_needed()
        headers = {
            'Client-ID': self.client_id,
            'Authorization': f'Bearer {self.api_token}'
        }
        subs = []
        url = 'https://api.twitch.tv/helix/eventsub/subscriptions'
        params = {}
        try:
            while True:
                r = requests.get(url, headers=headers, params=params, timeout=10)
                if r.status_code != 200:
                    logger.debug(f"GET subscriptions failed: {r.status_code} {r.text}")
                    break
                data = r.json()
                subs.extend(data.get('data', []))
                pagination = data.get('pagination', {})
                cursor = pagination.get('cursor')
                if not cursor:
                    break
                params['after'] = cursor
        except Exception as e:
            logger.debug(f"Error fetching subscriptions: {e}")
        return subs

    async def get_eventsub_subscriptions(self):
        """Fetch all EventSub subscriptions via the Helix API (async wrapper)."""
        return await asyncio.get_event_loop().run_in_executor(None, self.get_eventsub_subscriptions_sync)

    async def delete_eventsub_subscription(self, subscription_id):
        """Delete a single EventSub subscription by ID and update local tracking."""
        self.refresh_token_if_needed()
        headers = {
            'Client-ID': self.client_id,
            'Authorization': f'Bearer {self.api_token}'
        }
        try:
            r = requests.delete(f'https://api.twitch.tv/helix/eventsub/subscriptions?id={subscription_id}', headers=headers, timeout=10)
            if r.status_code in (204, 200):
                logger.info(f"Deleted EventSub subscription {subscription_id}")
                # cleanup local maps
                if subscription_id in self.subscription_to_streamer:
                    streamer, event_type = self.subscription_to_streamer.pop(subscription_id)
                    key = f"{streamer}_{event_type}"
                    if key in self.subscription_ids:
                        del self.subscription_ids[key]
                    cost = self.subscription_costs.get(event_type, 1)
                    self.current_subscription_cost = max(0, self.current_subscription_cost - cost)
                if subscription_id in self.subscription_session:
                    del self.subscription_session[subscription_id]
                return True
            else:
                logger.warning(f"Failed to delete subscription {subscription_id}: {r.status_code} {r.text}")
                return False
        except Exception as e:
            logger.error(f"Exception deleting subscription {subscription_id}: {e}")
            return False

    async def cleanup_stale_websocket_subscriptions(self, max_keep_sessions=2):
        """
        Attempt to delete 'stale' websocket subscriptions (failed/disconnected) until the number
        of websocket transports is at or below max_keep_sessions.
        """
        subs = await self.get_eventsub_subscriptions()
        # find websocket subscriptions
        ws_subs = [s for s in subs if s.get('transport', {}).get('method') == 'websocket' and s.get('transport', {}).get('session_id')]
        session_map = {}
        for s in ws_subs:
            sid = s['transport'].get('session_id')
            session_map.setdefault(sid, []).append(s)
        active_sessions = list(session_map.keys())
        logger.info(f"Found websocket sessions: {active_sessions} (total {len(active_sessions)})")
        # Attempt to delete stale ones first (status indicates failure/disconnect)
        candidates = []
        for sid, items in session_map.items():
            for it in items:
                status = it.get('status', '')
                disconnected_at = it.get('transport', {}).get('disconnected_at')
                if status in ('websocket_failed_ping_pong', 'websocket_disconnected') or disconnected_at:
                    candidates.append(it)
        # Sort candidates by disconnected_at (oldest first)
        candidates.sort(key=lambda x: x.get('transport', {}).get('disconnected_at') or "")
        deletions = 0
        for c in candidates:
            # Re-evaluate active sessions count
            subs_now = await self.get_eventsub_subscriptions()
            session_ids_now = {s.get('transport', {}).get('session_id') for s in subs_now if s.get('transport', {}).get('method') == 'websocket' and s.get('transport', {}).get('session_id')}
            if len(session_ids_now) <= max_keep_sessions:
                break
            sid = c.get('transport', {}).get('session_id')
            sub_id = c.get('id')
            if sub_id:
                ok = await self.delete_eventsub_subscription(sub_id)
                if ok:
                    deletions += 1
        logger.info(f"Cleanup complete, deleted {deletions} stale subscriptions")
        return deletions

    def is_connection_alive(self):
        """
        Simple method to check if the EventSub connection appears to be alive.
        Uses multiple fallback methods to avoid attribute errors.
        """
        if not self.eventsub_connection:
            return False

        try:
            # Try to access connection state safely
            if hasattr(self.eventsub_connection, 'state'):
                # Check if state indicates open connection
                state_str = str(self.eventsub_connection.state)
                return 'OPEN' in state_str.upper()

            # Fallback: if we have recent session info, assume connection is alive
            if self.current_session_id and self.connection_established_time:
                connection_age = datetime.now() - self.connection_established_time
                return connection_age.total_seconds() < 1800  # Less than 30 minutes

            return False

        except Exception:
            # If we can't determine state, use session info as fallback
            return bool(self.current_session_id and self.connection_established_time)

    async def health_check(self):
        """
        Comprehensive health check for the EventSub WebSocket connection and notification system.
        Returns a detailed status report about the connection, subscriptions, and potential issues.
        """
        health_status = {
            'overall_status': 'UNKNOWN',
            'timestamp': datetime.now().isoformat(),
            'connection': {},
            'subscriptions': {},
            'token': {},
            'notifications': {},
            'issues': [],
            'recommendations': []
        }

        try:
            # Check WebSocket connection status using safe method
            if self.is_connection_alive():
                health_status['connection']['status'] = 'CONNECTED'
                health_status['connection']['session_id'] = self.current_session_id

                if self.connection_established_time:
                    uptime = datetime.now() - self.connection_established_time
                    health_status['connection']['uptime_seconds'] = int(uptime.total_seconds())
                    health_status['connection']['uptime_formatted'] = str(uptime).split('.')[0]

                # Check last keepalive
                if self.last_keepalive_time:
                    keepalive_age = datetime.now() - self.last_keepalive_time
                    health_status['connection']['last_keepalive_seconds_ago'] = int(keepalive_age.total_seconds())

                    if keepalive_age.total_seconds() > 600:  # 10 minutes
                        health_status['issues'].append("No keepalive received in over 10 minutes - connection may be stale")
                        health_status['recommendations'].append("Consider restarting the EventSub connection")
                else:
                    health_status['issues'].append("No keepalive messages received yet")
            else:
                health_status['connection']['status'] = 'DISCONNECTED'
                health_status['issues'].append("EventSub WebSocket connection is not active")
                health_status['recommendations'].append("Check network connectivity and restart the bot")

                # If we have a recent session ID but no connection object, it might be a temporary state
                if self.current_session_id and self.connection_established_time:
                    connection_age = datetime.now() - self.connection_established_time
                    if connection_age.total_seconds() < 300:  # Less than 5 minutes
                        health_status['connection']['status'] = 'RECONNECTING'
                        health_status['issues'][-1] = "EventSub connection may be reconnecting"

            # Check EventSub task status
            if hasattr(self, '_eventsub_task'):
                if self._eventsub_task and not self._eventsub_task.done():
                    health_status['connection']['task_status'] = 'RUNNING'
                else:
                    health_status['connection']['task_status'] = 'STOPPED'
                    health_status['issues'].append("EventSub background task is not running")
                    health_status['recommendations'].append("Restart the EventSub connection task")

            # Check token validity
            token_valid = self.validate_token()
            health_status['token']['valid'] = token_valid

            if token_valid:
                time_until_expiry = self.token_expires_at - datetime.now()
                health_status['token']['expires_in_seconds'] = int(time_until_expiry.total_seconds())
                health_status['token']['expires_at'] = self.token_expires_at.isoformat()

                if time_until_expiry.total_seconds() < 3600:  # Less than 1 hour
                    health_status['issues'].append("OAuth token expires in less than 1 hour")
                    health_status['recommendations'].append("Token will be auto-refreshed, but monitor for refresh failures")
            else:
                health_status['issues'].append("OAuth token is invalid or expired")
                health_status['recommendations'].append("Check token refresh mechanism or update tokens manually")

            # Check subscriptions
            try:
                all_subs = await self.get_eventsub_subscriptions()
                active_subs = [s for s in all_subs if s.get('status') == 'enabled']
                websocket_subs = [s for s in active_subs if s.get('transport', {}).get('method') == 'websocket']

                health_status['subscriptions']['total_active'] = len(active_subs)
                health_status['subscriptions']['websocket_subscriptions'] = len(websocket_subs)
                health_status['subscriptions']['expected_subscriptions'] = len(self.streamers) * 3
                health_status['subscriptions']['current_cost'] = self.current_subscription_cost
                health_status['subscriptions']['max_cost'] = self.max_subscription_cost

                # Check for missing subscriptions
                expected_subs = len(self.streamers) * 3  # 3 events per streamer
                if len(websocket_subs) < expected_subs:
                    missing = expected_subs - len(websocket_subs)
                    health_status['issues'].append(f"Missing {missing} expected subscriptions")
                    health_status['recommendations'].append("Restart EventSub connection to recreate missing subscriptions")

                # Check for failed subscriptions
                failed_subs = [s for s in all_subs if s.get('status') in ['websocket_failed_ping_pong', 'websocket_disconnected']]
                if failed_subs:
                    health_status['subscriptions']['failed_subscriptions'] = len(failed_subs)
                    health_status['issues'].append(f"{len(failed_subs)} subscriptions have failed")
                    health_status['recommendations'].append("Clean up failed subscriptions and recreate them")

                # Check subscription distribution by streamer
                streamer_subs = {}
                for sub in websocket_subs:
                    user_id = sub.get('condition', {}).get('broadcaster_user_id')
                    if user_id:
                        streamer_subs[user_id] = streamer_subs.get(user_id, 0) + 1

                health_status['subscriptions']['per_streamer'] = streamer_subs

            except Exception as e:
                health_status['issues'].append(f"Failed to fetch subscription status: {str(e)}")
                health_status['recommendations'].append("Check API connectivity and token permissions")

            # Check notification activity
            if self.last_notification_time:
                notification_age = datetime.now() - self.last_notification_time
                health_status['notifications']['last_notification_seconds_ago'] = int(notification_age.total_seconds())
                health_status['notifications']['last_notification_time'] = self.last_notification_time.isoformat()

                # If no notifications for a very long time, it might indicate an issue
                if notification_age.total_seconds() > 86400:  # 24 hours
                    health_status['issues'].append("No notifications sent in over 24 hours - may indicate connection issues")
                    health_status['recommendations'].append("Verify streamers are active and subscriptions are working")
            else:
                health_status['notifications']['last_notification_time'] = None
                health_status['issues'].append("No notifications have been sent since bot startup")

            # Check stream status consistency
            live_streamers = [s for s, status in self.stream_status.items() if status['live']]
            health_status['notifications']['currently_live_streamers'] = live_streamers
            health_status['notifications']['total_tracked_streamers'] = len(self.streamers)

            # Determine overall status
            if not health_status['issues']:
                health_status['overall_status'] = 'HEALTHY'
            elif len(health_status['issues']) <= 2:
                health_status['overall_status'] = 'WARNING'
            else:
                health_status['overall_status'] = 'CRITICAL'

            return health_status

        except Exception as e:
            health_status['overall_status'] = 'ERROR'
            health_status['issues'].append(f"Health check failed: {str(e)}")
            logger.error(f"Health check error: {e}")
            return health_status

    @commands.command(name="health_check")
    async def health_check_command(self, ctx):
        """Command to perform and display health check results"""
        if not self.is_mod(ctx):
            return

        health = await self.health_check()

        # Create a summary message
        status_emoji = {
            'HEALTHY': '✅',
            'WARNING': '⚠️',
            'CRITICAL': '❌',
            'ERROR': '💥',
            'UNKNOWN': '❓'
        }

        emoji = status_emoji.get(health['overall_status'], '❓')
        connection_status = health['connection'].get('status', 'UNKNOWN')
        active_subs = health['subscriptions'].get('websocket_subscriptions', 0)
        expected_subs = health['subscriptions'].get('expected_subscriptions', 0)

        summary = f"{emoji} Health: {health['overall_status']} | Connection: {connection_status} | Subs: {active_subs}/{expected_subs}"

        if health['issues']:
            issues_text = " | Issues: " + "; ".join(health['issues'][:2])  # Show first 2 issues
            if len(health['issues']) > 2:
                issues_text += f" (+{len(health['issues'])-2} more)"
            summary += issues_text

        await ctx.send(summary)

        # Log detailed health status
        logger.info(f"Health Check Results: {json.dumps(health, indent=2, default=str)}")

    @commands.command(name="restart_eventsub")
    async def restart_eventsub_command(self, ctx):
        """Command to manually restart the EventSub connection"""
        if not self.is_mod(ctx):
            return

        try:
            await ctx.send("🔄 Restarting EventSub connection...")

            # Stop current connection
            self._eventsub_stop = True
            if self.eventsub_connection:
                await self.eventsub_connection.close()

            # Wait a moment for cleanup
            await asyncio.sleep(2)

            # Restart connection
            await self.start_eventsub_connection()

            await ctx.send("✅ EventSub connection restart initiated")
            logger.info(f"EventSub connection manually restarted by {ctx.author.name}")

        except Exception as e:
            await ctx.send(f"❌ Failed to restart EventSub: {str(e)}")
            logger.error(f"Failed to restart EventSub connection: {e}")

    async def diagnose_notification_issues(self):
        """
        Diagnose potential issues that could cause notifications to stop working
        after streams are offline for extended periods.
        """
        issues = []

        # Check for stale subscriptions
        try:
            subs = await self.get_eventsub_subscriptions()
            stale_subs = [s for s in subs if s.get('status') in ['websocket_failed_ping_pong', 'websocket_disconnected']]

            if stale_subs:
                issues.append(f"Found {len(stale_subs)} stale/failed subscriptions that need cleanup")

            # Check for too many websocket transports
            ws_sessions = {s.get('transport', {}).get('session_id') for s in subs
                          if s.get('transport', {}).get('method') == 'websocket' and s.get('transport', {}).get('session_id')}

            if len(ws_sessions) >= 3:
                issues.append(f"Too many websocket sessions ({len(ws_sessions)}/3) - may hit Twitch limits")

        except Exception as e:
            issues.append(f"Failed to check subscriptions: {e}")

        # Check token expiry
        if datetime.now() >= self.token_expires_at - timedelta(hours=1):
            issues.append("OAuth token is close to expiry or expired")

        # Check connection age
        if self.connection_established_time:
            connection_age = datetime.now() - self.connection_established_time
            if connection_age.total_seconds() > 86400:  # 24 hours
                issues.append("EventSub connection is over 24 hours old - consider restart")

        # Check for missing keepalives
        if self.last_keepalive_time:
            keepalive_age = datetime.now() - self.last_keepalive_time
            if keepalive_age.total_seconds() > 600:  # 10 minutes
                issues.append("No keepalive received in over 10 minutes - connection may be dead")

        return issues

if __name__ == "__main__":
    try:
        bot = TwitchBot()
        logger.info("Starting TwitchBot...")
        bot.run()
    except KeyboardInterrupt:
        logger.info("Shutting down TwitchBot...")
    except Exception as e:
        logger.error(f"Fatal error starting TwitchBot: {e}")