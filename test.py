import requests
import datetime

def get_stream_start_time(streamer):
    """Get stream start time from Twitch API"""
    url = f"https://api.twitch.tv/helix/streams?user_login={streamer}"
    headers = {
        'Authorization': f'Bearer qoa1v4n1aclif9x9hbf9sa7nko80mt',
        'Client-Id': "gp762nuuoqcoxypju8c569th9wz7q5"
    }
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        stream_data = response.json()
        
        if stream_data.get('data'):
            stream_info = stream_data['data'][0]
            started_at = stream_info.get('started_at')
            if started_at:
                return datetime.fromisoformat(started_at.replace('Z', '+00:00')).replace(tzinfo=None)
        
        return None
    except requests.RequestException as e:
        print(f"Failed to get stream start time for {streamer}: {e}")
        return None

if __name__ == "__main__":
    get_stream_start_time("not_xenotv")
