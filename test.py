import requests
from datetime import datetime

def get_stream_start_time(streamer):
    """Get stream start time from Twitch API"""
    url = f"https://api.twitch.tv/helix/streams?user_login={streamer}"
    headers = {
        'Authorization': f'Bearer qoa1v4n1aclif9x9hbf9sa7nko80mt',
        'Client-Id': "gp762nuuoqcoxypju8c569th9wz7q5"
    }
    try:
        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()
        stream_data = response.json()
        
        if stream_data.get('data'):
            stream_info = stream_data['data'][0]
            started_at = stream_info.get('started_at')
            if started_at:
                return datetime.fromisoformat(started_at.replace('Z', '+00:00')).replace(tzinfo=None)
        
        return None
    except requests.RequestException as e:
        print(f"Failed to get stream start time for {streamer}: {e}")
        return None
    
def format_uptime(self, start_time, end_time):
    """Format uptime in 'Xstd Ymin' format"""
    if not start_time or not end_time:
        return "unbekannt"
    
    duration = end_time - start_time
    total_seconds = int(duration.total_seconds())
    
    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60
    
    if hours > 0:
        return f"{hours}std {minutes}min"
    else:
        return f"{minutes}min"

if __name__ == "__main__":
    result = get_stream_start_time("not_xenotv")
    current_uptime = format_uptime(result, datetime.now())
    print(f"{current_uptime}")
