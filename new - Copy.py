import time
from twitchio.ext import commands
import json
import asyncio
import requests
import websockets
import logging
from datetime import datetime, timedelta
from typing import Optional


# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TwitchBot(commands.Bot):

    def __init__(self):
        with open("config.json") as config_file:
            config = json.load(config_file)
        
        self.oauth_token = config["oauth_token"]
        self.client_id = config["client_id"]
        self.refresh_token = config.get("refresh_token")
        self.streamers = config["streamers"]
        
        # Use the OAuth token for API calls (no client_secret needed)
        self.api_token = self.oauth_token
        self.token_expires_at = datetime.now() + timedelta(seconds=3600)  # Default expiry
        
        # Validate the token on startup
        if not self.validate_token():
            logger.info("Token invalid or expired, attempting to refresh...")
            if not self.refresh_oauth_token():
                logger.error("Failed to refresh token. Please update your OAuth token in config.json")
                raise Exception("Failed to refresh OAuth token")

        super().__init__(token=self.oauth_token, prefix="!", initial_channels=self.streamers)

        # Initialisiere Status-Tracker für Streamer
        self.stream_status = {streamer: {'live': False, 'title': '', 'game': ''} for streamer in self.streamers}

        # Single EventSub WebSocket connection for all streamers
        self.eventsub_connection = None
        self.reconnect_delay = 5  # Start with 5 seconds
        self.max_reconnect_delay = 300  # Maximum 5 minutes
        self.subscription_ids = {}  # Track subscription IDs for cleanup
        
        # Map to track which subscription belongs to which streamer
        self.subscription_to_streamer = {}

        # Dynamische Nachrichten für Benachrichtigungen
        self.notification_messages = {
            'live': lambda streamer: f"{streamer} ist jetzt live! Pag",
            'offline': lambda streamer: f"{streamer} ist offline gegangen. FeelsBadMan",
            'title': lambda streamer, new_title: f"{streamer} hat den Titel geändert! Neuer Titel PauseChamp 👉 {new_title}  ",
            'game': lambda streamer, new_game: f"{streamer} hat die Kategorie geändert PauseChamp 👉 {new_game}!"
        }

        # Nachrichten für den !notification Command
        self.command_messages = {
            'add_notification_live': lambda user, streamer: f"@{user}, du wirst benachrichtigt, wenn {streamer} live geht!",
            'add_notification_offline': lambda user, streamer: f"{user}, du wirst benachrichtigt, wenn {streamer} offline geht!",
            'add_notification_title': lambda user, streamer: f"{user}, du wirst benachrichtigt, wenn {streamer} den Titel ändert!",
            'add_notification_game': lambda user, streamer: f"{user}, du wirst benachrichtigt, wenn {streamer} das Spiel wechselt!",
            'already_notified_live': lambda user, streamer: f"{user}, du wirst bereits benachrichtigt, wenn {streamer} live geht!",
            'already_notified_offline': lambda user, streamer: f"{user}, du wirst bereits benachrichtigt, wenn {streamer} offline geht!",
            'already_notified_title': lambda user, streamer: f"{user}, du wirst bereits benachrichtigt, wenn {streamer} den Titel ändert!",
            'already_notified_game': lambda user, streamer: f"{user}, du wirst bereits benachrichtigt, wenn {streamer} das Spiel wechselt!",
            'invalid_type': lambda notification_type: f"Ungültiger Benachrichtigungstyp: {notification_type}. Versuche: live, offline, title, game.",
            'add_all': lambda user, streamer: f"{user}, du wirst über alle Aktivitäten von {streamer} benachrichtigt!",
            'remove_notification': lambda user, streamer, notification_type: f"{user}, du wirst nicht mehr benachrichtigt, wenn {streamer} {notification_type}.",
            'remove_all_notifications': lambda user, streamer: f"{user}, du wirst nicht mehr über Aktivitäten von {streamer} benachrichtigt!"
        }

    def validate_token(self):
        """Validate the current OAuth token"""
        url = "https://id.twitch.tv/oauth2/validate"
        headers = {
            'Authorization': f'Bearer {self.api_token}'
        }
        try:
            response = requests.get(url, headers=headers, timeout=10)
            if response.status_code == 200:
                token_info = response.json()
                # Update token expiry time if provided
                expires_in = token_info.get('expires_in')
                if expires_in:
                    self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                logger.info(f"Token valid. Expires in {expires_in} seconds")
                return True
            else:
                logger.warning(f"Token validation failed: {response.status_code}")
                return False
        except requests.RequestException as e:
            logger.error(f"Failed to validate token: {e}")
            return False

    def refresh_oauth_token(self):
        """Refresh the OAuth token using the refresh token"""
        if not self.refresh_token:
            logger.error("No refresh token available")
            return False
            
        url = "https://id.twitch.tv/oauth2/token"
        data = {
            'client_id': self.client_id,
            'refresh_token': self.refresh_token,
            'grant_type': 'refresh_token'
        }
        try:
            response = requests.post(url, data=data, timeout=10)
            response.raise_for_status()
            response_data = response.json()
            
            if 'access_token' in response_data:
                self.api_token = response_data['access_token']
                self.oauth_token = self.api_token  # Update the OAuth token too
                
                # Update refresh token if provided
                if 'refresh_token' in response_data:
                    self.refresh_token = response_data['refresh_token']
                    # Optionally update config.json with new tokens
                    self.update_config_tokens()
                
                expires_in = response_data.get('expires_in', 3600)
                self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
                
                logger.info("Successfully refreshed OAuth token")
                return True
            else:
                logger.error(f"No access token in refresh response: {response_data}")
                return False
        except requests.RequestException as e:
            logger.error(f"Failed to refresh token: {e}")
            return False

    def update_config_tokens(self):
        """Update the config.json file with new tokens"""
        try:
            with open("config.json", 'r') as f:
                config = json.load(f)
            
            config["oauth_token"] = self.oauth_token
            if self.refresh_token:
                config["refresh_token"] = self.refresh_token
            
            with open("config.json", 'w') as f:
                json.dump(config, f, indent=4)
            
            logger.info("Updated config.json with new tokens")
        except Exception as e:
            logger.error(f"Failed to update config.json: {e}")

    def get_api_token(self):
        """Get app access token from Twitch API - Not needed for user OAuth"""
        # This method is not needed anymore since we use user OAuth token
        return self.api_token

    def refresh_token_if_needed(self):
        """Refresh the OAuth token if it's about to expire"""
        if datetime.now() >= self.token_expires_at - timedelta(minutes=5):
            logger.info("Token is about to expire, refreshing...")
            if self.refresh_oauth_token():
                logger.info("Token refreshed successfully")
            else:
                logger.error("Failed to refresh token")
                # Try to validate current token as fallback
                if not self.validate_token():
                    logger.error("Current token is also invalid")

    def get_user_id(self, streamer):
        """Get user ID for a streamer"""
        self.refresh_token_if_needed()
        url = f"https://api.twitch.tv/helix/users?login={streamer}"
        headers = {
            'Authorization': f'Bearer {self.api_token}',
            'Client-Id': self.client_id
        }
        try:
            response = requests.get(url, headers=headers, timeout=10)
            response.raise_for_status()
            user_data = response.json()
            if 'data' in user_data and user_data['data']:
                return user_data['data'][0]['id']
        except requests.RequestException as e:
            logger.error(f"Failed to get user ID for {streamer}: {e}")
        return None

    async def create_eventsub_subscription(self, session_id, streamer, user_id, event_type):
        """Create EventSub subscription for a specific event type"""
        self.refresh_token_if_needed()
        
        subscription_data = {
            "type": event_type,
            "version": "1" if event_type in ["stream.online", "stream.offline"] else "2",
            "condition": {
                "broadcaster_user_id": user_id
            },
            "transport": {
                "method": "websocket",
                "session_id": session_id
            }
        }

        headers = {
            'Client-ID': self.client_id,
            'Authorization': f'Bearer {self.api_token}',
            'Content-Type': 'application/json'
        }

        try:
            response = requests.post(
                'https://api.twitch.tv/helix/eventsub/subscriptions',
                headers=headers, 
                data=json.dumps(subscription_data),
                timeout=10
            )
            
            if response.status_code == 202:
                subscription_data = response.json()
                subscription_id = subscription_data['data'][0]['id']
                
                # Track the subscription
                key = f"{streamer}_{event_type}"
                self.subscription_ids[key] = subscription_id
                self.subscription_to_streamer[subscription_id] = (streamer, event_type)
                
                logger.info(f"Successfully subscribed to {event_type} for {streamer} (ID: {subscription_id})")
                return True
            else:
                logger.error(f"Failed to subscribe to {event_type} for {streamer}: {response.text}")
                return False
        except requests.RequestException as e:
            logger.error(f"Request failed for {event_type} subscription for {streamer}: {e}")
            return False

    async def handle_eventsub_message(self, message_data):
        """Handle incoming EventSub messages"""
        message_type = message_data['metadata']['message_type']
        
        if message_type == 'notification':
            subscription_id = message_data['payload']['subscription']['id']
            
            # Find which streamer this notification belongs to
            if subscription_id in self.subscription_to_streamer:
                streamer, event_type = self.subscription_to_streamer[subscription_id]
            else:
                logger.warning(f"Received notification for unknown subscription ID: {subscription_id}")
                return
            
            event = message_data['payload']['event']
            subscription_type = message_data['metadata']['subscription_type']
            
            logger.info(f"Received {subscription_type} event for {streamer}")
            
            if subscription_type == 'stream.online':
                if not self.stream_status[streamer]['live']:
                    self.stream_status[streamer]['live'] = True
                    await self.notify_users(streamer, 'live')
                    logger.info(f"{streamer} went live")
                    
            elif subscription_type == 'stream.offline':
                if self.stream_status[streamer]['live']:
                    self.stream_status[streamer]['live'] = False
                    await self.notify_users(streamer, 'offline')
                    logger.info(f"{streamer} went offline")
                    
            elif subscription_type == 'channel.update':
                new_title = event.get('title', '')
                new_game = event.get('category_name', '')
                
                # Check for title change
                if new_title != self.stream_status[streamer]['title']:
                    logger.info(f"{streamer} changed title to: {new_title}")
                    await self.notify_users(streamer, 'title', new_title=new_title)
                    self.stream_status[streamer]['title'] = new_title
                
                # Check for game change
                if new_game != self.stream_status[streamer]['game']:
                    logger.info(f"{streamer} changed game to: {new_game}")
                    await self.notify_users(streamer, 'game', new_game=new_game)
                    self.stream_status[streamer]['game'] = new_game
                    
        elif message_type == 'session_keepalive':
            logger.debug(f"Keepalive received")
            
        elif message_type == 'session_reconnect':
            reconnect_url = message_data['payload']['session']['reconnect_url']
            logger.info(f"Reconnect requested to {reconnect_url}")
            # The connection will be closed and reconnected automatically
            
        elif message_type == 'revocation':
            subscription_id = message_data['payload']['subscription']['id']
            if subscription_id in self.subscription_to_streamer:
                streamer, event_type = self.subscription_to_streamer[subscription_id]
                logger.warning(f"Subscription revoked for {streamer} ({event_type}): {message_data['payload']}")
                # Clean up tracking
                del self.subscription_to_streamer[subscription_id]
                key = f"{streamer}_{event_type}"
                if key in self.subscription_ids:
                    del self.subscription_ids[key]

    async def connect_eventsub(self):
        """Connect to EventSub WebSocket for all streamers"""
        try:
            async with websockets.connect('wss://eventsub.wss.twitch.tv/ws') as ws:
                logger.info("Connected to EventSub WebSocket")
                self.eventsub_connection = ws
                
                # Reset reconnect delay on successful connection
                self.reconnect_delay = 5
                
                # Receive welcome message
                welcome_msg = await ws.recv()
                welcome_data = json.loads(welcome_msg)
                
                if welcome_data['metadata']['message_type'] != 'session_welcome':
                    logger.error(f"Unexpected welcome message: {welcome_data}")
                    return
                
                session_id = welcome_data['payload']['session']['id']
                logger.info(f"EventSub session established with ID: {session_id}")
                
                # Subscribe to events for all streamers
                events_to_subscribe = ['stream.online', 'stream.offline', 'channel.update']
                
                for streamer in self.streamers:
                    user_id = self.get_user_id(streamer)
                    if not user_id:
                        logger.error(f"Failed to get user ID for {streamer}")
                        continue
                    
                    for event_type in events_to_subscribe:
                        await self.create_eventsub_subscription(session_id, streamer, user_id, event_type)
                        # Add a small delay to avoid rate limiting
                        await asyncio.sleep(0.1)
                
                # Listen for messages
                async for message in ws:
                    try:
                        message_data = json.loads(message)
                        await self.handle_eventsub_message(message_data)
                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to parse EventSub message: {e}")
                    except Exception as e:
                        logger.error(f"Error handling EventSub message: {e}")
                        
        except websockets.exceptions.ConnectionClosed as e:
            logger.warning(f"EventSub connection closed: {e}")
        except websockets.exceptions.WebSocketException as e:
            logger.error(f"EventSub WebSocket error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error in EventSub connection: {e}")
        finally:
            # Clean up connection reference
            self.eventsub_connection = None
            
            # Clear subscription tracking
            self.subscription_ids.clear()
            self.subscription_to_streamer.clear()
            
            # Schedule reconnection with exponential backoff
            logger.info(f"Reconnecting to EventSub in {self.reconnect_delay} seconds")
            await asyncio.sleep(self.reconnect_delay)
            
            # Increase reconnect delay for next time (exponential backoff)
            self.reconnect_delay = min(self.reconnect_delay * 2, self.max_reconnect_delay)
            
            # Reconnect
            asyncio.create_task(self.connect_eventsub())

    async def start_eventsub_connection(self):
        """Start EventSub connection for all streamers"""
        asyncio.create_task(self.connect_eventsub())

    async def get_initial_stream_status(self):
        """Get initial stream status for all streamers using API"""
        for streamer in self.streamers:
            try:
                # Get stream data
                url = f"https://api.twitch.tv/helix/streams?user_login={streamer}"
                headers = {
                    'Authorization': f'Bearer {self.api_token}',
                    'Client-Id': self.client_id
                }
                response = requests.get(url, headers=headers, timeout=10)
                stream_data = response.json()

                # Get channel data
                user_id = self.get_user_id(streamer)
                if user_id:
                    channel_url = f"https://api.twitch.tv/helix/channels?broadcaster_id={user_id}"
                    channel_response = requests.get(channel_url, headers=headers, timeout=10)
                    channel_data = channel_response.json()
                else:
                    channel_data = {'data': []}

                # Update status
                if stream_data.get('data'):
                    stream_info = stream_data['data'][0]
                    self.stream_status[streamer] = {
                        'live': True,
                        'title': stream_info.get('title', ''),
                        'game': stream_info.get('game_name', '')
                    }
                elif channel_data.get('data'):
                    channel_info = channel_data['data'][0]
                    self.stream_status[streamer] = {
                        'live': False,
                        'title': channel_info.get('title', ''),
                        'game': channel_info.get('game_name', '')
                    }
                else:
                    self.stream_status[streamer] = {
                        'live': False,
                        'title': '',
                        'game': ''
                    }
                    
                logger.info(f"Initial status for {streamer}: {self.stream_status[streamer]}")
                
            except Exception as e:
                logger.error(f"Failed to get initial status for {streamer}: {e}")

    async def event_ready(self):
        logger.info(f"Logged in as {self.nick}")
        
        # Get initial stream status
        await self.get_initial_stream_status()
        
        # Start single EventSub connection
        await self.start_eventsub_connection()

    async def event_message(self, message):
        # Sicherstellen, dass message.author nicht None ist
        if not message.author:
            return

        # Prüfen, ob message.author.name existiert und ob der Inhalt der Nachricht übereinstimmt
        if message.author.name.lower() == "streamelements" and "OUAH? 😯  Xeno ist live und ich habe nicht mal mitbekommen??? 😔" in message.content:
            logger.info(f"Sending response to StreamElements in {message.channel.name}")
            await asyncio.sleep(5)
            await message.channel.send("@StreamElements, Keine Sorge mein nutzloser Freund! STARMAN benutze einfach den Command !notification in kombination mit: all, live, offline, title, game um bei entsprechener aktivität benachrichtigt zu werden!")
            return

        await self.handle_commands(message)

    @commands.command(name='notification')
    async def notification(self, ctx, *, notification_type: Optional[str] = None):
        streamer = ctx.channel.name
        file_name = f"{streamer}_notifications.json"

        try:
            with open(file_name, 'r') as f:
                notifications = json.load(f)
        except FileNotFoundError:
            notifications = {}

        user = ctx.author.name

        if not notification_type:
            await ctx.send(self.command_messages['invalid_type'](''))
            return

        if notification_type == 'all':
            notifications[user] = ['live', 'offline', 'title', 'game']
            await ctx.send(self.command_messages['add_all'](user, streamer))
        else:
            if notification_type not in ['live', 'offline', 'title', 'game']:
                await ctx.send(self.command_messages['invalid_type'](notification_type))
                return

            if user not in notifications:
                notifications[user] = []

            if notification_type not in notifications[user]:
                notifications[user].append(notification_type)

                if notification_type == 'live':
                    await ctx.send(self.command_messages['add_notification_live'](user, streamer))
                elif notification_type == 'offline':
                    await ctx.send(self.command_messages['add_notification_offline'](user, streamer))
                elif notification_type == 'title':
                    await ctx.send(self.command_messages['add_notification_title'](user, streamer))
                elif notification_type == 'game':
                    await ctx.send(self.command_messages['add_notification_game'](user, streamer))
            else:
                if notification_type == 'live':
                    await ctx.send(self.command_messages['already_notified_live'](user, streamer))
                elif notification_type == 'offline':
                    await ctx.send(self.command_messages['already_notified_offline'](user, streamer))
                elif notification_type == 'title':
                    await ctx.send(self.command_messages['already_notified_title'](user, streamer))
                elif notification_type == 'game':
                    await ctx.send(self.command_messages['already_notified_game'](user, streamer))

        with open(file_name, 'w') as f:
            json.dump(notifications, f)

    async def notify_users(self, streamer, notification_type, new_title=None, new_game=None):
        user_mentions = []
        file_name = f"{streamer}_notifications.json"

        try:
            with open(file_name, 'r') as f:
                notifications = json.load(f)
        except FileNotFoundError:
            notifications = {}
    
        for user, types in notifications.items():
            if notification_type in types:
                user_mentions.append(f"@{user}")
                
        channel = self.get_channel(streamer)
        if user_mentions and channel:
            mentions = " ".join(user_mentions)
            if notification_type == 'live':
                await channel.send(f"{self.notification_messages['live'](streamer)}")
                await channel.send(f"{mentions}")
            elif notification_type == 'offline':
                await channel.send(f"{self.notification_messages['offline'](streamer)}")
                await channel.send(f"{mentions}")
            elif notification_type == 'title':
                await channel.send(f"{self.notification_messages['title'](streamer, new_title)}")
                await channel.send(f"{mentions}")
            elif notification_type == 'game':
                await channel.send(f"{self.notification_messages['game'](streamer, new_game)}")
                await channel.send(f"{mentions}")

    @commands.command(name="sr")
    async def sr_command(self, ctx):
        if not (ctx.author.is_mod or ctx.author.is_vip):
            await ctx.send(f"@{ctx.author.name}, Huh? 🤔🎯 Wirklich? Du hast den Song-Request-Command ausprobiert? 🫵😆🎶 Sorry, aber der ist nur für Moderatoren & VIPs. 🙄🛑👑 Wir müssen natürlich nichts zahlen! 😎💅 Aber hey, wenn du unbedingt willst, kannst du ja deine 250 Kanalpunkte 💰🔥 dafür ausgeben 😏💸 certified skill issue 🤷‍♂️🎉")

    @commands.command(name="eventsub_status")
    async def eventsub_status(self, ctx):
        """Command to check EventSub connection status"""
        if not ctx.author.is_mod:
            return
            
        connection_status = "connected" if self.eventsub_connection else "disconnected"
        subscription_count = len(self.subscription_ids)
        expected_subscriptions = len(self.streamers) * 3  # 3 events per streamer
        
        await ctx.send(f"EventSub Status: {connection_status}, Subscriptions: {subscription_count}/{expected_subscriptions}")

if __name__ == "__main__":
    bot = TwitchBot()
    bot.run()