import asyncio
import websockets
import json
import requests
import time

# Deine App-Daten
CLIENT_ID = 'vpb172qnn5447une2hn1rnodqz2902'
CLIENT_SECRET = 'wkylf1gg5vvyk6rgmvns96ns901svy'
BROADCASTER_LOGIN = 'not_xenotv'  # z.B. "shroud"

# OAuth Access Token besorgen
def get_app_access_token():
    url = 'https://id.twitch.tv/oauth2/token'
    params = {
        'client_id': CLIENT_ID,
        'client_secret': CLIENT_SECRET,
        'grant_type': 'client_credentials'
    }
    resp = requests.post(url, params=params).json()
    return resp['access_token']

# User-ID eines Streamers holen
def get_broadcaster_user_id(access_token, username):
    headers = {
        'Client-ID': CLIENT_ID,
        'Authorization': f'Bearer {access_token}'
    }
    url = f'https://api.twitch.tv/helix/users?login={username}'
    resp = requests.get(url, headers=headers).json()
    return resp['data'][0]['id']

async def listen_for_updates():
    access_token = "dbwlf3119rswbpl0a3mwrcgq50bmgs"
    user_id = get_broadcaster_user_id(access_token, BROADCASTER_LOGIN)

    async with websockets.connect('wss://eventsub.wss.twitch.tv/ws') as ws:
        hello_msg = await ws.recv()
        hello_data = json.loads(hello_msg)
        session_id = hello_data['payload']['session']['id']

        print(f"[INFO] Verbunden mit Session ID: {session_id}")

        # Sub für channel.update senden
        subscription = {
            "type": "channel.update",
            "version": "2",
            "condition": {
                "broadcaster_user_id": user_id
            },
            "transport": {
                "method": "websocket",
                "session_id": session_id
            }
        }

        headers = {
            'Client-ID': CLIENT_ID,
            'Authorization': f'Bearer {access_token}',
            'Content-Type': 'application/json'
        }

        resp = requests.post('https://api.twitch.tv/helix/eventsub/subscriptions',
                             headers=headers, data=json.dumps(subscription))

        if resp.status_code != 202:
            print(f"[ERROR] Subscription fehlgeschlagen: {resp.text}")
            return
        else:
            print("[INFO] Subscription erfolgreich.")

        # Nachrichten empfangen
        while True:
            msg = await ws.recv()
            data = json.loads(msg)

            if data['metadata']['message_type'] == 'notification':
                event = data['payload']['event']
                title = event.get('title', '(kein Titel)')
                game = event.get('category_name', '(kein Spiel)')
                print(f"\n🎯 Neuer Titel: {title}")
                print(f"🎮 Neue Kategorie: {game}")
            elif data['metadata']['message_type'] == 'session_keepalive':
                print("[PING] Keepalive erhalten")
            elif data['metadata']['message_type'] == 'revocation':
                print("[WARNUNG] Subscription wurde widerrufen.")
                break

# Ausführen
asyncio.run(listen_for_updates())
